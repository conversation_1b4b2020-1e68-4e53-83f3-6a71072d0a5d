import 'package:flutter/material.dart';
import 'package:pointycastle/export.dart' as pc;
import 'package:yo_merchant_payment/models/api_request.dart';
import 'package:yo_merchant_payment/models/user_settings.dart';
import 'package:yo_merchant_payment/utils/crypto_utils.dart';
import 'package:yo_merchant_payment/utils/storage_utils.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  static const routeName = '/';

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
    UserSettings? userSettings;

    final TextEditingController _userInputController = TextEditingController();

    void _addUserInput() {
      final userInput = _userInputController.text.trim();

      final nonce = generateNonce();


      if(userInput.isEmpty) return;
      if(userSettings == null) return;

      final apiRequest = ApiRequest(
        userSettings: userSettings!,
        transactionTime: getDateTime(),
        userInput: userInput
      );

      const privateKeyPem = '''
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
        ''';

      await storage.write(key: 'private_key', value: privateKeyPem);

      final pem = await storage.read(key: 'private_key');

      if (pem != null) {
        final rsaPrivateKey = parsePrivateKeyFromPem(pem);
        // TODO: Use the rsaPrivateKey for cryptographic operations
      }

    }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments as UserSettings?;

    userSettings = args ?? userSettings;
    return Scaffold(
        appBar: AppBar(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            title: Text('Yo! Merchant Payment Test'),
            actions: [
                IconButton(
                    onPressed: () {
                        Navigator.of(context).pushNamed('/add-settings');
                    }, 
                    icon: const Icon(Icons.settings)
                )
            ],
        ),
        body: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    flex: 1,
                    child: userSettings == null ? const Text('No settings added yet') :
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            Text('Phone: ${userSettings?.phoneNumber}'),
                            Text('USSD Code: ${userSettings?.ussdServiceCode} '),
                            Text('Session ID: ${userSettings?.sessionId} '),
                            SizedBox(
                                height: 30,
                            ),
                            Text('RESULTS DISPLAY', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
                            Expanded(
                              child: Container(
                                  color: Colors.black,    
                              ),
                            )   
                        ],
                    )
                ),
                
                SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: TextField(
                          controller: _userInputController,
                          decoration: InputDecoration(labelText: 'User Input'),
                        ),
                    ),
                    SizedBox(width: 5),
                    ElevatedButton(
                        onPressed: () {}, 
                        child: Text('Send')
                    )
                  ],
                ),
    
              ],
            ),
        ),
    );
  }
}