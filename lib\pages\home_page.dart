import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:yo_merchant_payment/models/api_request.dart';
import 'package:yo_merchant_payment/models/user_settings.dart';
import 'package:yo_merchant_payment/utils/crypto_utils.dart';
import 'package:yo_merchant_payment/utils/storage_utils.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  static const routeName = '/';

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
    UserSettings? userSettings;

    final TextEditingController _userInputController = TextEditingController();

    Future<void> _addUserInput() async {
      final userInput = _userInputController.text.trim();

      if(userInput.isEmpty) return;
      if(userSettings == null) return;

      try {
        // Generate nonce for this request
        final nonce = generateNonce();

        // Create API request
        final apiRequest = ApiRequest(
          userSettings: userSettings!,
          transactionTime: getDateTime(),
          userInput: userInput
        );

        // Load or create private key
        const privateKeyPem = '''***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************''';

        await storage.write(key: 'private_key', value: privateKeyPem);

        final pem = await storage.read(key: 'private_key');

        if (pem != null) {
          final rsaPrivateKey = parsePrivateKeyFromPem(pem);

          // Create data to sign (JSON string of the API request)
          final dataToSign = utf8.encode(jsonEncode(apiRequest.toJson())) + utf8.encode(nonce);

          // Sign the data
          final signature = await signData(Uint8List.fromList(dataToSign), rsaPrivateKey);
          final signatureBase64 = base64Encode(signature);

          var apikey = '100000';
          var apiSecret = '0+d4s48d89Ds*7d_2dosd';

          var encodedSecretKey = utf8.encode(apiSecret);

          final message = apikey + nonce + 



          // Create HMAC
          final hmacKey = utf8.encode(''); 
          final hmac = Hmac(sha256, hmacKey);
          final hmacDigest = hmac.convert(dataToSign);
          final hmacHex = hmacDigest.toString();



          
          
        }
      } catch (e) {
        print('Error sending API request: $e');
      }
    }

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments as UserSettings?;

    userSettings = args ?? userSettings;
    return Scaffold(
        appBar: AppBar(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
            title: Text('Yo! Merchant Payment Test'),
            actions: [
                IconButton(
                    onPressed: () {
                        Navigator.of(context).pushNamed('/add-settings');
                    }, 
                    icon: const Icon(Icons.settings)
                )
            ],
        ),
        body: Padding(
            padding: EdgeInsets.all(16),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    flex: 1,
                    child: userSettings == null ? const Text('No settings added yet') :
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            Text('Phone: ${userSettings?.phoneNumber}'),
                            Text('USSD Code: ${userSettings?.ussdServiceCode} '),
                            Text('Session ID: ${userSettings?.sessionId} '),
                            SizedBox(
                                height: 30,
                            ),
                            Text('RESULTS DISPLAY', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),),
                            Expanded(
                              child: Container(
                                  color: Colors.black,    
                              ),
                            )   
                        ],
                    )
                ),
                
                SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                        flex: 1,
                        child: TextField(
                          controller: _userInputController,
                          decoration: InputDecoration(labelText: 'User Input'),
                        ),
                    ),
                    SizedBox(width: 5),
                    ElevatedButton(
                        onPressed: () {}, 
                        child: Text('Send')
                    )
                  ],
                ),
    
              ],
            ),
        ),
    );
  }
}